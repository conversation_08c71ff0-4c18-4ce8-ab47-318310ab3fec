import React from 'react';
import { Check, <PERSON>, Crown } from 'lucide-react';
import { useLanguage } from '../context/LanguageContext';
import { subscriptionPackages } from '../data/mockData';

interface SubscriptionsPageProps {
  onNavigate: (page: string, data?: any) => void;
}

const SubscriptionsPage: React.FC<SubscriptionsPageProps> = ({ onNavigate }) => {
  const { language, t } = useLanguage();

  const handleSelectPackage = (pkg: typeof subscriptionPackages[0]) => {
    onNavigate('order', {
      serviceType: 'subscription',
      package: pkg,
      serviceName: language === 'ar' ? pkg.name : pkg.nameEn
    });
  };

  return (
    <div className="min-h-screen bg-gray-50 pt-20">
      {/* Header Section */}
      <section className="bg-gradient-to-br from-indigo-600 to-purple-600 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="flex justify-center mb-6">
            <div className="bg-white/20 backdrop-blur-md border border-white/30 rounded-full p-4">
              <Crown className="h-8 w-8 text-white" />
            </div>
          </div>
          <h1 className="text-4xl md:text-5xl font-bold mb-6">
            {t('subscriptionsTitle')}
          </h1>
          <p className="text-xl text-indigo-100 max-w-2xl mx-auto">
            {language === 'ar' 
              ? 'اختر الباقة المناسبة لك واستمتع بجميع مميزات تيليجرام بريميوم'
              : 'Choose the package that suits you and enjoy all Telegram Premium features'
            }
          </p>
        </div>
      </section>

      {/* Packages Section */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {subscriptionPackages.map((pkg, index) => (
              <div
                key={pkg.id}
                className={`relative bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 ${
                  pkg.popular 
                    ? 'border-2 border-indigo-500 ring-4 ring-indigo-100' 
                    : 'border border-gray-200'
                }`}
              >
                {/* Popular Badge */}
                {pkg.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <div className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-6 py-2 rounded-full text-sm font-semibold flex items-center space-x-2 rtl:space-x-reverse">
                      <Star className="h-4 w-4 fill-current" />
                      <span>{t('mostPopular')}</span>
                    </div>
                  </div>
                )}

                <div className="p-8">
                  {/* Package Header */}
                  <div className="text-center mb-8">
                    <h3 className="text-2xl font-bold text-gray-900 mb-2">
                      {language === 'ar' ? pkg.name : pkg.nameEn}
                    </h3>
                    <p className="text-gray-600 mb-4">
                      {language === 'ar' ? pkg.duration : pkg.durationEn}
                    </p>
                    
                    {pkg.price > 0 ? (
                      <div className="flex items-center justify-center space-x-1 rtl:space-x-reverse">
                        <span className="text-4xl font-bold text-indigo-600">
                          ${pkg.price}
                        </span>
                        <span className="text-gray-500">USD</span>
                      </div>
                    ) : (
                      <div className="text-2xl font-bold text-indigo-600">
                        {language === 'ar' ? 'حسب الطلب' : 'Custom Price'}
                      </div>
                    )}
                  </div>

                  {/* Features */}
                  <div className="space-y-4 mb-8">
                    {(language === 'ar' ? pkg.features : pkg.featuresEn).map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-start space-x-3 rtl:space-x-reverse">
                        <div className="bg-green-100 rounded-full p-1 mt-0.5">
                          <Check className="h-4 w-4 text-green-600" />
                        </div>
                        <span className="text-gray-700 leading-relaxed">{feature}</span>
                      </div>
                    ))}
                  </div>

                  {/* CTA Button */}
                  <button
                    onClick={() => handleSelectPackage(pkg)}
                    className={`w-full py-4 rounded-xl font-semibold text-lg transition-all duration-300 transform hover:scale-105 ${
                      pkg.popular
                        ? 'bg-gradient-to-r from-indigo-600 to-purple-600 text-white hover:from-indigo-700 hover:to-purple-700 shadow-lg'
                        : 'bg-gray-100 text-gray-900 hover:bg-gray-200'
                    }`}
                  >
                    {t('selectPackage')}
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Comparison */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              {language === 'ar' ? 'مميزات تيليجرام بريميوم' : 'Telegram Premium Features'}
            </h2>
            <div className="w-24 h-1 bg-gradient-to-r from-indigo-600 to-purple-600 mx-auto rounded-full"></div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[
              {
                title: language === 'ar' ? 'رفع ملفات 4GB' : 'Upload 4GB Files',
                description: language === 'ar' ? 'رفع ملفات كبيرة الحجم' : 'Upload large files'
              },
              {
                title: language === 'ar' ? 'سرعة مضاعفة' : 'Double Speed',
                description: language === 'ar' ? 'تحميل أسرع' : 'Faster downloads'
              },
              {
                title: language === 'ar' ? 'ملصقات مميزة' : 'Premium Stickers',
                description: language === 'ar' ? 'مجموعة حصرية' : 'Exclusive collection'
              },
              {
                title: language === 'ar' ? 'مجلدات متقدمة' : 'Advanced Folders',
                description: language === 'ar' ? 'تنظيم أفضل' : 'Better organization'
              }
            ].map((feature, index) => (
              <div key={index} className="text-center p-6 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors duration-200">
                <div className="bg-gradient-to-br from-indigo-100 to-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Crown className="h-8 w-8 text-indigo-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {feature.title}
                </h3>
                <p className="text-gray-600 text-sm">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
};

export default SubscriptionsPage;
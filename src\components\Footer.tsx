import React from 'react';
import { Zap, Shield, Clock, Award } from 'lucide-react';
import { useLanguage } from '../context/LanguageContext';

const Footer: React.FC = () => {
  const { language, t } = useLanguage();

  return (
    <footer className="bg-gradient-to-br from-indigo-900 via-purple-900 to-indigo-800 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Brand */}
          <div className="col-span-1 md:col-span-2">
            <div className="flex items-center space-x-2 rtl:space-x-reverse mb-4">
              <div className="bg-white/20 p-2 rounded-xl">
                <Zap className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-2xl font-bold">
                {language === 'ar' ? 'متجر تيليجرام' : 'Telegram Store'}
              </h3>
            </div>
            <p className="text-indigo-100 leading-relaxed max-w-md">
              {t('footerDesc')}
            </p>
            
            {/* Features */}
            <div className="grid grid-cols-2 gap-4 mt-6">
              <div className="flex items-center space-x-2 rtl:space-x-reverse">
                <Shield className="h-5 w-5 text-green-400" />
                <span className="text-sm text-indigo-100">
                  {language === 'ar' ? 'دفع آمن' : 'Secure Payment'}
                </span>
              </div>
              <div className="flex items-center space-x-2 rtl:space-x-reverse">
                <Clock className="h-5 w-5 text-blue-400" />
                <span className="text-sm text-indigo-100">
                  {language === 'ar' ? 'تفعيل سريع' : 'Fast Activation'}
                </span>
              </div>
              <div className="flex items-center space-x-2 rtl:space-x-reverse">
                <Award className="h-5 w-5 text-yellow-400" />
                <span className="text-sm text-indigo-100">
                  {language === 'ar' ? 'جودة عالية' : 'High Quality'}
                </span>
              </div>
              <div className="flex items-center space-x-2 rtl:space-x-reverse">
                <Zap className="h-5 w-5 text-purple-400" />
                <span className="text-sm text-indigo-100">
                  {language === 'ar' ? 'دعم 24/7' : '24/7 Support'}
                </span>
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-lg font-semibold mb-4">{t('quickLinks')}</h4>
            <ul className="space-y-2">
              {[
                { key: 'home', label: t('home') },
                { key: 'subscriptions', label: t('subscriptions') },
                { key: 'stars', label: t('stars') },
                { key: 'faq', label: t('faq') },
              ].map((link) => (
                <li key={link.key}>
                  <span className="text-indigo-200 hover:text-white transition-colors duration-200 cursor-pointer">
                    {link.label}
                  </span>
                </li>
              ))}
            </ul>
          </div>

          {/* Payment Methods */}
          <div>
            <h4 className="text-lg font-semibold mb-4">{t('paymentMethods')}</h4>
            <div className="space-y-3">
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                  <span className="text-xs font-bold">₮</span>
                </div>
                <span className="text-indigo-200">USDT (TRC20)</span>
              </div>
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                  <span className="text-xs font-bold">💎</span>
                </div>
                <span className="text-indigo-200">TON</span>
              </div>
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                  <span className="text-xs font-bold">$</span>
                </div>
                <span className="text-indigo-200">USDC</span>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom */}
        <div className="border-t border-indigo-700 mt-8 pt-8 text-center">
          <p className="text-indigo-200">
            © 2024 {language === 'ar' ? 'متجر تيليجرام' : 'Telegram Store'}. 
            {language === 'ar' ? ' جميع الحقوق محفوظة.' : ' All rights reserved.'}
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
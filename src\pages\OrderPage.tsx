import React, { useState, useEffect } from 'react';
import { ArrowLeft, ArrowRight, Upload, Copy, Check, AlertCircle } from 'lucide-react';
import { useLanguage } from '../context/LanguageContext';
import { generateQRCode } from '../utils/qrcode';
import { sendOrderToTelegram, validateTelegramId } from '../utils/telegram';
import { walletAddresses } from '../data/mockData';

interface OrderPageProps {
  orderData: any;
  onNavigate: (page: string) => void;
}

const OrderPage: React.FC<OrderPageProps> = ({ orderData, onNavigate }) => {
  const { language, t } = useLanguage();
  const [formData, setFormData] = useState({
    telegramId: '',
    customAmount: '',
    paymentMethod: 'USDT' as 'USDT' | 'TON' | 'USDC',
    paymentProof: null as File | null,
    txId: ''
  });
  const [qrCode, setQrCode] = useState('');
  const [copied, setCopied] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const isRTL = language === 'ar';
  const BackIcon = isRTL ? ArrowRight : ArrowLeft;

  // Calculate total price
  const calculateTotal = () => {
    if (orderData.serviceType === 'stars') {
      return orderData.package.price;
    }
    if (orderData.serviceType === 'subscription') {
      if (orderData.package.id === 'custom') {
        return parseFloat(formData.customAmount) || 0;
      }
      return orderData.package.price;
    }
    return 0;
  };

  // Generate QR code when payment method changes
  useEffect(() => {
    const walletAddress = walletAddresses[formData.paymentMethod];
    if (walletAddress) {
      generateQRCode(walletAddress).then(setQrCode);
    }
  }, [formData.paymentMethod]);

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.telegramId) {
      newErrors.telegramId = t('required');
    } else if (!validateTelegramId(formData.telegramId)) {
      newErrors.telegramId = t('invalidTelegramId');
    }

    if (orderData.package.id === 'custom' && !formData.customAmount) {
      newErrors.customAmount = t('required');
    }

    if (!formData.paymentProof && !formData.txId) {
      newErrors.payment = language === 'ar' 
        ? 'يجب رفع إثبات الدفع أو إدخال معرف المعاملة'
        : 'Must upload payment proof or enter transaction ID';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsSubmitting(true);
    
    try {
      await sendOrderToTelegram({
        serviceType: orderData.serviceType === 'subscription' ? 'اشتراك تيليجرام' : 'نجوم تيليجرام',
        packageName: orderData.serviceName,
        quantity: orderData.serviceType === 'stars' ? orderData.package.amount : 1,
        telegramId: formData.telegramId,
        paymentMethod: formData.paymentMethod,
        totalPrice: calculateTotal()
      });

      alert(t('orderSuccess'));
      onNavigate('home');
    } catch (error) {
      alert(t('orderError'));
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!orderData) {
    return (
      <div className="min-h-screen bg-gray-50 pt-20 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            {language === 'ar' ? 'لم يتم اختيار خدمة' : 'No Service Selected'}
          </h2>
          <button
            onClick={() => onNavigate('home')}
            className="bg-indigo-600 text-white px-6 py-3 rounded-lg hover:bg-indigo-700"
          >
            {language === 'ar' ? 'العودة للرئيسية' : 'Back to Home'}
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 pt-20">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={() => onNavigate(orderData.serviceType === 'subscription' ? 'subscriptions' : 'stars')}
            className="flex items-center space-x-2 rtl:space-x-reverse text-indigo-600 hover:text-indigo-700 mb-4"
          >
            <BackIcon className="h-5 w-5" />
            <span>{language === 'ar' ? 'العودة' : 'Back'}</span>
          </button>
          
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            {t('orderTitle')}
          </h1>
          <p className="text-gray-600">
            {orderData.serviceName}
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Order Form */}
          <div className="bg-white rounded-2xl shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">
              {language === 'ar' ? 'تفاصيل الطلب' : 'Order Details'}
            </h2>

            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Telegram ID */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t('telegramId')} *
                </label>
                <input
                  type="text"
                  value={formData.telegramId}
                  onChange={(e) => handleInputChange('telegramId', e.target.value)}
                  placeholder={t('telegramIdPlaceholder')}
                  className={`w-full px-4 py-3 border rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 ${
                    errors.telegramId ? 'border-red-500' : 'border-gray-300'
                  }`}
                />
                {errors.telegramId && (
                  <p className="text-red-500 text-sm mt-1">{errors.telegramId}</p>
                )}
              </div>

              {/* Custom Amount (for custom packages) */}
              {orderData.package.id === 'custom' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {language === 'ar' ? 'المبلغ (USD)' : 'Amount (USD)'} *
                  </label>
                  <input
                    type="number"
                    min="1"
                    step="0.01"
                    value={formData.customAmount}
                    onChange={(e) => handleInputChange('customAmount', e.target.value)}
                    className={`w-full px-4 py-3 border rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 ${
                      errors.customAmount ? 'border-red-500' : 'border-gray-300'
                    }`}
                  />
                  {errors.customAmount && (
                    <p className="text-red-500 text-sm mt-1">{errors.customAmount}</p>
                  )}
                </div>
              )}

              {/* Payment Method */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t('paymentMethod')} *
                </label>
                <div className="grid grid-cols-3 gap-3">
                  {(['USDT', 'TON', 'USDC'] as const).map((method) => (
                    <button
                      key={method}
                      type="button"
                      onClick={() => handleInputChange('paymentMethod', method)}
                      className={`p-3 border-2 rounded-xl text-center font-medium transition-all duration-200 ${
                        formData.paymentMethod === method
                          ? 'border-indigo-500 bg-indigo-50 text-indigo-700'
                          : 'border-gray-300 hover:border-gray-400'
                      }`}
                    >
                      {method}
                    </button>
                  ))}
                </div>
              </div>

              {/* Payment Proof */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t('uploadProof')}
                </label>
                <input
                  type="file"
                  accept="image/*"
                  onChange={(e) => handleInputChange('paymentProof', e.target.files?.[0] || null)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                />
              </div>

              {/* Transaction ID */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t('txId')}
                </label>
                <input
                  type="text"
                  value={formData.txId}
                  onChange={(e) => handleInputChange('txId', e.target.value)}
                  placeholder={language === 'ar' ? 'معرف المعاملة' : 'Transaction ID'}
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                />
              </div>

              {errors.payment && (
                <div className="flex items-center space-x-2 rtl:space-x-reverse text-red-600 bg-red-50 p-3 rounded-lg">
                  <AlertCircle className="h-5 w-5" />
                  <span className="text-sm">{errors.payment}</span>
                </div>
              )}

              {/* Submit Button */}
              <button
                type="submit"
                disabled={isSubmitting}
                className="w-full bg-gradient-to-r from-indigo-600 to-purple-600 text-white py-4 rounded-xl font-semibold text-lg hover:from-indigo-700 hover:to-purple-700 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? (
                  <span className="flex items-center justify-center space-x-2 rtl:space-x-reverse">
                    <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    <span>{language === 'ar' ? 'جاري الإرسال...' : 'Submitting...'}</span>
                  </span>
                ) : (
                  t('submitOrder')
                )}
              </button>
            </form>
          </div>

          {/* Payment Information */}
          <div className="space-y-6">
            {/* Order Summary */}
            <div className="bg-white rounded-2xl shadow-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                {language === 'ar' ? 'ملخص الطلب' : 'Order Summary'}
              </h3>
              
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">{language === 'ar' ? 'الخدمة:' : 'Service:'}</span>
                  <span className="font-medium">{orderData.serviceName}</span>
                </div>
                
                {orderData.serviceType === 'stars' && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">{language === 'ar' ? 'الكمية:' : 'Quantity:'}</span>
                    <span className="font-medium">{orderData.package.amount.toLocaleString()}</span>
                  </div>
                )}
                
                <div className="border-t pt-3">
                  <div className="flex justify-between text-lg font-semibold">
                    <span>{language === 'ar' ? 'المجموع:' : 'Total:'}</span>
                    <span className="text-indigo-600">${calculateTotal().toFixed(2)}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Payment Instructions */}
            <div className="bg-white rounded-2xl shadow-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                {language === 'ar' ? 'تعليمات الدفع' : 'Payment Instructions'}
              </h3>

              {/* Wallet Address */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t('walletAddress')} ({formData.paymentMethod})
                </label>
                <div className="flex items-center space-x-2 rtl:space-x-reverse">
                  <input
                    type="text"
                    value={walletAddresses[formData.paymentMethod]}
                    readOnly
                    className="flex-1 px-3 py-2 bg-gray-50 border border-gray-300 rounded-lg text-sm font-mono"
                  />
                  <button
                    type="button"
                    onClick={() => copyToClipboard(walletAddresses[formData.paymentMethod])}
                    className="p-2 text-indigo-600 hover:bg-indigo-50 rounded-lg transition-colors duration-200"
                  >
                    {copied ? <Check className="h-5 w-5" /> : <Copy className="h-5 w-5" />}
                  </button>
                </div>
              </div>

              {/* QR Code */}
              {qrCode && (
                <div className="text-center">
                  <p className="text-sm text-gray-600 mb-3">
                    {language === 'ar' ? 'أو امسح الرمز:' : 'Or scan the code:'}
                  </p>
                  <img src={qrCode} alt="QR Code" className="mx-auto border rounded-lg" />
                </div>
              )}
            </div>

            {/* Important Notes */}
            <div className="bg-yellow-50 border border-yellow-200 rounded-2xl p-6">
              <h4 className="font-semibold text-yellow-800 mb-3">
                {language === 'ar' ? 'ملاحظات مهمة:' : 'Important Notes:'}
              </h4>
              <ul className="text-sm text-yellow-700 space-y-2">
                <li>• {language === 'ar' 
                  ? 'تأكد من إرسال المبلغ الصحيح بالضبط'
                  : 'Make sure to send the exact amount'
                }</li>
                <li>• {language === 'ar'
                  ? 'احتفظ بإثبات الدفع أو معرف المعاملة'
                  : 'Keep payment proof or transaction ID'
                }</li>
                <li>• {language === 'ar'
                  ? 'سيتم التفعيل خلال 5-15 دقيقة'
                  : 'Activation within 5-15 minutes'
                }</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderPage;
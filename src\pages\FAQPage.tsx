import React, { useState } from 'react';
import { Plus, Minus, HelpCircle } from 'lucide-react';
import { useLanguage } from '../context/LanguageContext';
import { faqs } from '../data/mockData';

const FAQPage: React.FC = () => {
  const { language, t } = useLanguage();
  const [openFAQ, setOpenFAQ] = useState<string | null>(null);

  const toggleFAQ = (id: string) => {
    setOpenFAQ(openFAQ === id ? null : id);
  };

  return (
    <div className="min-h-screen bg-gray-50 pt-20">
      {/* Header Section */}
      <section className="bg-gradient-to-br from-indigo-600 to-purple-600 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="flex justify-center mb-6">
            <div className="bg-white/20 backdrop-blur-md border border-white/30 rounded-full p-4">
              <HelpCircle className="h-8 w-8 text-white" />
            </div>
          </div>
          <h1 className="text-4xl md:text-5xl font-bold mb-6">
            {t('faqTitle')}
          </h1>
          <p className="text-xl text-indigo-100 max-w-2xl mx-auto">
            {language === 'ar' 
              ? 'ابحث عن إجابات للأسئلة الأكثر شيوعاً حول خدماتنا'
              : 'Find answers to the most frequently asked questions about our services'
            }
          </p>
        </div>
      </section>

      {/* Search Section */}
      <section className="py-12 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="relative">
            <input
              type="text"
              placeholder={language === 'ar' ? 'ابحث في الأسئلة...' : 'Search questions...'}
              className="w-full px-6 py-4 text-lg border border-gray-300 rounded-2xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 shadow-sm"
            />
            <HelpCircle className="absolute right-4 rtl:left-4 rtl:right-auto top-1/2 transform -translate-y-1/2 h-6 w-6 text-gray-400" />
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="space-y-4">
            {faqs.map((faq) => (
              <div
                key={faq.id}
                className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden transition-all duration-300 hover:shadow-xl"
              >
                <button
                  onClick={() => toggleFAQ(faq.id)}
                  className="w-full px-6 py-6 text-left focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-inset"
                >
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold text-gray-900 pr-8 rtl:pl-8 rtl:pr-0">
                      {language === 'ar' ? faq.question : faq.questionEn}
                    </h3>
                    <div className="flex-shrink-0">
                      {openFAQ === faq.id ? (
                        <Minus className="h-6 w-6 text-indigo-600" />
                      ) : (
                        <Plus className="h-6 w-6 text-gray-400" />
                      )}
                    </div>
                  </div>
                </button>
                
                {openFAQ === faq.id && (
                  <div className="px-6 pb-6">
                    <div className="border-t border-gray-200 pt-4">
                      <p className="text-gray-700 leading-relaxed">
                        {language === 'ar' ? faq.answer : faq.answerEn}
                      </p>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Additional Help Section */}
      <section className="py-16 bg-gradient-to-br from-indigo-50 to-purple-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-6">
            {language === 'ar' ? 'لم تجد ما تبحث عنه؟' : "Didn't find what you're looking for?"}
          </h2>
          <p className="text-gray-600 mb-8 max-w-2xl mx-auto">
            {language === 'ar'
              ? 'فريق الدعم الفني لدينا متاح 24/7 لمساعدتك في أي استفسار'
              : 'Our technical support team is available 24/7 to help you with any inquiry'
            }
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-3xl mx-auto">
            <div className="bg-white rounded-xl p-6 shadow-lg">
              <div className="bg-blue-100 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-4">
                <HelpCircle className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">
                {language === 'ar' ? 'دعم فوري' : 'Instant Support'}
              </h3>
              <p className="text-gray-600 text-sm">
                {language === 'ar' ? 'احصل على المساعدة فوراً' : 'Get help instantly'}
              </p>
            </div>
            
            <div className="bg-white rounded-xl p-6 shadow-lg">
              <div className="bg-green-100 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-green-600 font-bold">💬</span>
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">
                {language === 'ar' ? 'دردشة مباشرة' : 'Live Chat'}
              </h3>
              <p className="text-gray-600 text-sm">
                {language === 'ar' ? 'تحدث مع خبرائنا' : 'Talk to our experts'}
              </p>
            </div>
            
            <div className="bg-white rounded-xl p-6 shadow-lg">
              <div className="bg-purple-100 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-purple-600 font-bold">📧</span>
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">
                {language === 'ar' ? 'البريد الإلكتروني' : 'Email Support'}
              </h3>
              <p className="text-gray-600 text-sm">
                {language === 'ar' ? 'أرسل استفسارك' : 'Send your inquiry'}
              </p>
            </div>
          </div>
          
          <button className="mt-8 bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-8 py-3 rounded-xl font-semibold hover:from-indigo-700 hover:to-purple-700 transition-all duration-300">
            {language === 'ar' ? 'تواصل معنا الآن' : 'Contact Us Now'}
          </button>
        </div>
      </section>
    </div>
  );
};

export default FAQPage;
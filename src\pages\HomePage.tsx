import React from 'react';
import { ArrowR<PERSON>, ArrowLeft, Shield, Zap, Award, Star, Users, Clock } from 'lucide-react';
import { useLanguage } from '../context/LanguageContext';

interface HomePageProps {
  onNavigate: (page: string) => void;
}

const HomePage: React.FC<HomePageProps> = ({ onNavigate }) => {
  const { language, t } = useLanguage();
  const isRTL = language === 'ar';
  const ArrowIcon = isRTL ? ArrowLeft : ArrowRight;

  const features = [
    {
      icon: Shield,
      title: language === 'ar' ? 'دفع آمن ومشفر' : 'Secure & Encrypted Payment',
      description: language === 'ar' 
        ? 'نستخدم أحدث تقنيات التشفير لحماية معاملاتك المالية'
        : 'We use the latest encryption technologies to protect your financial transactions'
    },
    {
      icon: Zap,
      title: language === 'ar' ? 'تفعيل فوري' : 'Instant Activation',
      description: language === 'ar'
        ? 'يتم تفعيل خدماتك خلال دقائق معدودة'
        : 'Your services are activated within minutes'
    },
    {
      icon: Award,
      title: language === 'ar' ? 'جودة مضمونة' : 'Guaranteed Quality',
      description: language === 'ar'
        ? 'نقدم أفضل الخدمات بجودة عالية وأسعار تنافسية'
        : 'We provide the best services with high quality and competitive prices'
    }
  ];

  const stats = [
    { number: '10K+', label: language === 'ar' ? 'عميل راضٍ' : 'Satisfied Customers' },
    { number: '99.9%', label: language === 'ar' ? 'معدل النجاح' : 'Success Rate' },
    { number: '24/7', label: language === 'ar' ? 'دعم فني' : 'Technical Support' },
    { number: '5★', label: language === 'ar' ? 'تقييم العملاء' : 'Customer Rating' }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-indigo-900 via-purple-900 to-indigo-800 text-white overflow-hidden">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="absolute inset-0">
          <div className="absolute top-10 left-10 w-72 h-72 bg-purple-500/20 rounded-full blur-3xl"></div>
          <div className="absolute bottom-10 right-10 w-96 h-96 bg-indigo-500/20 rounded-full blur-3xl"></div>
        </div>
        
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 md:py-32">
          <div className="text-center max-w-4xl mx-auto">
            <div className="flex justify-center mb-6">
              <div className="bg-white/10 backdrop-blur-md border border-white/20 rounded-full px-6 py-2">
                <span className="text-sm font-medium flex items-center space-x-2 rtl:space-x-reverse">
                  <Star className="h-4 w-4 text-yellow-400" />
                  <span>{language === 'ar' ? 'الأفضل في المنطقة' : 'Best in the Region'}</span>
                </span>
              </div>
            </div>
            
            <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
              <span className="bg-gradient-to-r from-white to-purple-200 bg-clip-text text-transparent">
                {t('heroTitle')}
              </span>
            </h1>
            
            <p className="text-xl md:text-2xl text-indigo-100 mb-8 leading-relaxed">
              {t('heroSubtitle')}
            </p>
            
            <p className="text-lg text-indigo-200 mb-12 max-w-2xl mx-auto">
              {t('heroDescription')}
            </p>

            <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-6 rtl:sm:space-x-reverse">
              <button
                onClick={() => onNavigate('subscriptions')}
                className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-8 py-4 rounded-xl font-semibold text-lg flex items-center space-x-2 rtl:space-x-reverse transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
              >
                <span>{t('getStarted')}</span>
                <ArrowIcon className="h-5 w-5" />
              </button>
              
              <button
                onClick={() => onNavigate('stars')}
                className="border-2 border-white/30 text-white hover:bg-white/10 px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 backdrop-blur-sm"
              >
                {t('buyStars')}
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-indigo-600 mb-2">
                  {stat.number}
                </div>
                <div className="text-gray-600 font-medium">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              {t('servicesTitle')}
            </h2>
            <div className="w-24 h-1 bg-gradient-to-r from-indigo-600 to-purple-600 mx-auto rounded-full"></div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Premium Subscription */}
            <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 border border-gray-100">
              <div className="bg-gradient-to-br from-indigo-100 to-purple-100 w-16 h-16 rounded-xl flex items-center justify-center mb-6">
                <Zap className="h-8 w-8 text-indigo-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">
                {t('premiumSubscription')}
              </h3>
              <p className="text-gray-600 mb-6 leading-relaxed">
                {t('premiumDesc')}
              </p>
              <button
                onClick={() => onNavigate('subscriptions')}
                className="w-full bg-gradient-to-r from-indigo-600 to-purple-600 text-white py-3 rounded-xl font-semibold hover:from-indigo-700 hover:to-purple-700 transition-all duration-300"
              >
                {language === 'ar' ? 'استكشف الباقات' : 'Explore Packages'}
              </button>
            </div>

            {/* Telegram Stars */}
            <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 border border-gray-100">
              <div className="bg-gradient-to-br from-yellow-100 to-orange-100 w-16 h-16 rounded-xl flex items-center justify-center mb-6">
                <Star className="h-8 w-8 text-yellow-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">
                {t('telegramStars')}
              </h3>
              <p className="text-gray-600 mb-6 leading-relaxed">
                {t('starsDesc')}
              </p>
              <button
                onClick={() => onNavigate('stars')}
                className="w-full bg-gradient-to-r from-yellow-500 to-orange-500 text-white py-3 rounded-xl font-semibold hover:from-yellow-600 hover:to-orange-600 transition-all duration-300"
              >
                {t('buyStars')}
              </button>
            </div>

            {/* Secure Payment */}
            <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 border border-gray-100">
              <div className="bg-gradient-to-br from-green-100 to-emerald-100 w-16 h-16 rounded-xl flex items-center justify-center mb-6">
                <Shield className="h-8 w-8 text-green-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">
                {t('securePay')}
              </h3>
              <p className="text-gray-600 mb-6 leading-relaxed">
                {t('securePayDesc')}
              </p>
              <button
                onClick={() => onNavigate('faq')}
                className="w-full bg-gradient-to-r from-green-600 to-emerald-600 text-white py-3 rounded-xl font-semibold hover:from-green-700 hover:to-emerald-700 transition-all duration-300"
              >
                {language === 'ar' ? 'تعلم المزيد' : 'Learn More'}
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-8">
                {language === 'ar' ? 'لماذا تختارنا؟' : 'Why Choose Us?'}
              </h2>
              
              <div className="space-y-6">
                {features.map((feature, index) => (
                  <div key={index} className="flex items-start space-x-4 rtl:space-x-reverse">
                    <div className="bg-gradient-to-br from-indigo-100 to-purple-100 p-3 rounded-xl flex-shrink-0">
                      <feature.icon className="h-6 w-6 text-indigo-600" />
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">
                        {feature.title}
                      </h3>
                      <p className="text-gray-600 leading-relaxed">
                        {feature.description}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="relative">
              <div className="bg-gradient-to-br from-indigo-600 to-purple-600 rounded-2xl p-8 text-white">
                <div className="absolute -top-4 -left-4 bg-yellow-400 rounded-full p-4">
                  <Award className="h-8 w-8 text-yellow-800" />
                </div>
                
                <div className="mt-4">
                  <h3 className="text-2xl font-bold mb-4">
                    {language === 'ar' ? 'ضمان الجودة' : 'Quality Guarantee'}
                  </h3>
                  <p className="text-indigo-100 mb-6 leading-relaxed">
                    {language === 'ar' 
                      ? 'نحن نضمن لك جودة الخدمة وسرعة التنفيذ مع إمكانية استرداد الأموال في حالة عدم الرضا'
                      : 'We guarantee service quality and fast execution with money-back guarantee if not satisfied'
                    }
                  </p>
                  
                  <div className="flex items-center space-x-4 rtl:space-x-reverse">
                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      <Users className="h-5 w-5" />
                      <span className="text-sm">10,000+ {language === 'ar' ? 'عميل' : 'Clients'}</span>
                    </div>
                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      <Clock className="h-5 w-5" />
                      <span className="text-sm">{language === 'ar' ? 'تفعيل فوري' : 'Instant'}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-br from-indigo-900 via-purple-900 to-indigo-800 text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            {language === 'ar' ? 'ابدأ رحلتك الآن' : 'Start Your Journey Now'}
          </h2>
          <p className="text-xl text-indigo-100 mb-8 max-w-2xl mx-auto">
            {language === 'ar' 
              ? 'انضم إلى آلاف العملاء الراضين واحصل على أفضل خدمات تيليجرام'
              : 'Join thousands of satisfied customers and get the best Telegram services'
            }
          </p>
          
          <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-6 rtl:sm:space-x-reverse">
            <button
              onClick={() => onNavigate('subscriptions')}
              className="bg-white text-indigo-900 px-8 py-4 rounded-xl font-semibold text-lg hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 shadow-lg"
            >
              {language === 'ar' ? 'اشترك الآن' : 'Subscribe Now'}
            </button>
            <button
              onClick={() => onNavigate('contact')}
              className="border-2 border-white/30 text-white hover:bg-white/10 px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300"
            >
              {language === 'ar' ? 'تواصل معنا' : 'Contact Us'}
            </button>
          </div>
        </div>
      </section>
    </div>
  );
};

export default HomePage;
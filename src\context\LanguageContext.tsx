import React, { createContext, useContext, useState, useEffect } from 'react';

interface LanguageContextType {
  language: 'ar' | 'en';
  toggleLanguage: () => void;
  t: (key: string) => string;
}

const translations = {
  ar: {
    // Navigation
    home: 'الرئيسية',
    subscriptions: 'الاشتراكات',
    stars: 'النجوم',
    reviews: 'التقييمات',
    faq: 'الأسئلة الشائعة',
    contact: 'اتصل بنا',
    
    // Home Page
    heroTitle: 'سوباجرام الاحترافي',
    heroSubtitle: 'احصل على اشتراك تيليجرام بريميوم ونجوم تيليجرام بأفضل الأسعار',
    heroDescription: 'نوفر لك خدمات تيليجرام المميزة بأسعار تنافسية ودفع آمن بالعملات الرقمية',
    getStarted: 'ابدأ الآن',
    
    // Services
    servicesTitle: 'خدماتنا المميزة',
    premiumSubscription: 'اشتراك تيليجرام بريميوم',
    premiumDesc: 'استمتع بجميع المميزات المتقدمة في تيليجرام',
    telegramStars: 'نجوم تيليجرام',
    starsDesc: 'اشتري نجوم تيليجرام لدعم المبدعين والبوتات',
    securePay: 'دفع آمن',
    securePayDesc: 'دفع بالعملات الرقمية مع حماية كاملة',
    
    // Subscriptions
    subscriptionsTitle: 'باقات الاشتراك',
    monthly: 'شهري',
    weekly: 'أسبوعي',
    custom: 'مخصص',
    mostPopular: 'الأكثر شعبية',
    selectPackage: 'اختر الباقة',
    
    // Stars
    starsTitle: 'نجوم تيليجرام',
    starsDescription: 'استخدم نجوم تيليجرام لدعم قنواتك المفضلة والبوتات التفاعلية',
    buyStars: 'شراء النجوم',
    
    // Order Form
    orderTitle: 'إكمال الطلب',
    telegramId: 'معرف تيليجرام',
    telegramIdPlaceholder: 'أدخل معرف تيليجرام الخاص بك',
    paymentMethod: 'طريقة الدفع',
    walletAddress: 'عنوان المحفظة',
    uploadProof: 'رفع إثبات الدفع',
    txId: 'معرف المعاملة (TXID)',
    submitOrder: 'إرسال الطلب',
    
    // Reviews
    reviewsTitle: 'تقييمات العملاء',
    
    // FAQ
    faqTitle: 'الأسئلة الشائعة',
    
    // Contact
    contactTitle: 'اتصل بنا',
    
    // Footer
    footerDesc: 'متجرك الموثوق لجميع خدمات تيليجرام المميزة',
    quickLinks: 'روابط سريعة',
    paymentMethods: 'طرق الدفع',
    
    // Messages
    orderSuccess: 'تم إرسال طلبك بنجاح! سيتم التواصل معك قريباً.',
    orderError: 'حدث خطأ في إرسال الطلب. يرجى المحاولة مرة أخرى.',
    
    // Form validation
    required: 'هذا الحقل مطلوب',
    invalidTelegramId: 'معرف تيليجرام غير صحيح',
  },
  en: {
    // Navigation
    home: 'Home',
    subscriptions: 'Subscriptions',
    stars: 'Stars',
    reviews: 'Reviews',
    faq: 'FAQ',
    contact: 'Contact',
    
    // Home Page
    heroTitle: 'Professional SubaGram',
    heroSubtitle: 'Get Telegram Premium subscriptions and Telegram Stars at the best prices',
    heroDescription: 'We provide you with premium Telegram services at competitive prices and secure payment with cryptocurrencies',
    getStarted: 'Get Started',
    
    // Services
    servicesTitle: 'Our Premium Services',
    premiumSubscription: 'Telegram Premium Subscription',
    premiumDesc: 'Enjoy all advanced features in Telegram',
    telegramStars: 'Telegram Stars',
    starsDesc: 'Buy Telegram Stars to support creators and bots',
    securePay: 'Secure Payment',
    securePayDesc: 'Pay with cryptocurrencies with full protection',
    
    // Subscriptions
    subscriptionsTitle: 'Subscription Packages',
    monthly: 'Monthly',
    weekly: 'Weekly',
    custom: 'Custom',
    mostPopular: 'Most Popular',
    selectPackage: 'Select Package',
    
    // Stars
    starsTitle: 'Telegram Stars',
    starsDescription: 'Use Telegram Stars to support your favorite channels and interactive bots',
    buyStars: 'Buy Stars',
    
    // Order Form
    orderTitle: 'Complete Order',
    telegramId: 'Telegram ID',
    telegramIdPlaceholder: 'Enter your Telegram ID',
    paymentMethod: 'Payment Method',
    walletAddress: 'Wallet Address',
    uploadProof: 'Upload Payment Proof',
    txId: 'Transaction ID (TXID)',
    submitOrder: 'Submit Order',
    
    // Reviews
    reviewsTitle: 'Customer Reviews',
    
    // FAQ
    faqTitle: 'Frequently Asked Questions',
    
    // Contact
    contactTitle: 'Contact Us',
    
    // Footer
    footerDesc: 'Your trusted store for all premium Telegram services',
    quickLinks: 'Quick Links',
    paymentMethods: 'Payment Methods',
    
    // Messages
    orderSuccess: 'Your order has been sent successfully! We will contact you soon.',
    orderError: 'An error occurred while sending the order. Please try again.',
    
    // Form validation
    required: 'This field is required',
    invalidTelegramId: 'Invalid Telegram ID',
  }
};

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export const LanguageProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [language, setLanguage] = useState<'ar' | 'en'>('ar');

  useEffect(() => {
    const savedLang = localStorage.getItem('language') as 'ar' | 'en' | null;
    if (savedLang) {
      setLanguage(savedLang);
    }
    
    // Update document attributes
    document.documentElement.lang = language;
    document.documentElement.dir = language === 'ar' ? 'rtl' : 'ltr';
  }, [language]);

  const toggleLanguage = () => {
    const newLang = language === 'ar' ? 'en' : 'ar';
    setLanguage(newLang);
    localStorage.setItem('language', newLang);
  };

  const t = (key: string): string => {
    return translations[language][key as keyof typeof translations['ar']] || key;
  };

  return (
    <LanguageContext.Provider value={{ language, toggleLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};
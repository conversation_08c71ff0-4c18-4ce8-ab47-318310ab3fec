import React from 'react';
import { Star, Gift, Sparkles } from 'lucide-react';
import { useLanguage } from '../context/LanguageContext';
import { starPackages } from '../data/mockData';

interface StarsPageProps {
  onNavigate: (page: string, data?: any) => void;
}

const StarsPage: React.FC<StarsPageProps> = ({ onNavigate }) => {
  const { language, t } = useLanguage();

  const handleSelectPackage = (pkg: typeof starPackages[0]) => {
    onNavigate('order', {
      serviceType: 'stars',
      package: pkg,
      serviceName: `${pkg.amount} ${language === 'ar' ? 'نجمة' : 'Stars'}`
    });
  };

  return (
    <div className="min-h-screen bg-gray-50 pt-20">
      {/* Header Section */}
      <section className="bg-gradient-to-br from-yellow-500 via-orange-500 to-pink-500 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="flex justify-center mb-6">
            <div className="bg-white/20 backdrop-blur-md border border-white/30 rounded-full p-4">
              <Star className="h-8 w-8 text-white fill-current" />
            </div>
          </div>
          <h1 className="text-4xl md:text-5xl font-bold mb-6">
            {t('starsTitle')}
          </h1>
          <p className="text-xl text-yellow-100 max-w-2xl mx-auto">
            {t('starsDescription')}
          </p>
        </div>
      </section>

      {/* What are Telegram Stars */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              {language === 'ar' ? 'ما هي نجوم تيليجرام؟' : 'What are Telegram Stars?'}
            </h2>
            <div className="w-24 h-1 bg-gradient-to-r from-yellow-500 to-orange-500 mx-auto rounded-full"></div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center p-6">
              <div className="bg-gradient-to-br from-yellow-100 to-orange-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Star className="h-8 w-8 text-yellow-600 fill-current" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                {language === 'ar' ? 'دعم المبدعين' : 'Support Creators'}
              </h3>
              <p className="text-gray-600">
                {language === 'ar' 
                  ? 'استخدم النجوم لدعم القنوات والمبدعين المفضلين لديك'
                  : 'Use stars to support your favorite channels and creators'
                }
              </p>
            </div>

            <div className="text-center p-6">
              <div className="bg-gradient-to-br from-blue-100 to-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Sparkles className="h-8 w-8 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                {language === 'ar' ? 'محتوى حصري' : 'Exclusive Content'}
              </h3>
              <p className="text-gray-600">
                {language === 'ar'
                  ? 'احصل على محتوى حصري ومميزات إضافية في القنوات'
                  : 'Get exclusive content and additional features in channels'
                }
              </p>
            </div>

            <div className="text-center p-6">
              <div className="bg-gradient-to-br from-green-100 to-emerald-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Gift className="h-8 w-8 text-green-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                {language === 'ar' ? 'مكافآت مجانية' : 'Free Rewards'}
              </h3>
              <p className="text-gray-600">
                {language === 'ar'
                  ? 'احصل على مكافآت إضافية مع الباقات الكبيرة'
                  : 'Get additional rewards with larger packages'
                }
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Stars Packages */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              {language === 'ar' ? 'باقات النجوم' : 'Stars Packages'}
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              {language === 'ar'
                ? 'اختر الكمية المناسبة من النجوم واحصل على مكافآت إضافية مع الباقات الكبيرة'
                : 'Choose the right amount of stars and get additional rewards with larger packages'
              }
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {starPackages.map((pkg) => (
              <div
                key={pkg.id}
                className="relative bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 border border-gray-200 overflow-hidden"
              >
                {pkg.bonus && (
                  <div className="absolute top-4 right-4 bg-gradient-to-r from-green-500 to-emerald-500 text-white px-3 py-1 rounded-full text-xs font-semibold">
                    +{pkg.bonus} {language === 'ar' ? 'مجاناً' : 'Free'}
                  </div>
                )}

                <div className="p-6">
                  <div className="text-center mb-6">
                    <div className="bg-gradient-to-br from-yellow-100 to-orange-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Star className="h-8 w-8 text-yellow-600 fill-current" />
                    </div>
                    
                    <div className="flex items-center justify-center space-x-2 rtl:space-x-reverse mb-2">
                      <span className="text-3xl font-bold text-gray-900">
                        {pkg.amount.toLocaleString()}
                      </span>
                      <Star className="h-6 w-6 text-yellow-500 fill-current" />
                    </div>
                    
                    {pkg.bonus && (
                      <div className="text-green-600 font-semibold text-sm mb-2">
                        + {pkg.bonus} {language === 'ar' ? 'نجمة مجانية' : 'Bonus Stars'}
                      </div>
                    )}
                    
                    <div className="flex items-center justify-center space-x-1 rtl:space-x-reverse">
                      <span className="text-2xl font-bold text-indigo-600">
                        ${pkg.price}
                      </span>
                      <span className="text-gray-500">USD</span>
                    </div>
                  </div>

                  <button
                    onClick={() => handleSelectPackage(pkg)}
                    className="w-full bg-gradient-to-r from-yellow-500 to-orange-500 text-white py-3 rounded-xl font-semibold hover:from-yellow-600 hover:to-orange-600 transition-all duration-300 transform hover:scale-105"
                  >
                    {t('buyStars')}
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* How to Use Stars */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              {language === 'ar' ? 'كيفية استخدام النجوم' : 'How to Use Stars'}
            </h2>
            <div className="w-24 h-1 bg-gradient-to-r from-yellow-500 to-orange-500 mx-auto rounded-full"></div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            {[
              {
                step: '1',
                title: language === 'ar' ? 'اشتري النجوم' : 'Buy Stars',
                description: language === 'ar' ? 'اختر الباقة وأكمل الدفع' : 'Choose package and complete payment'
              },
              {
                step: '2',
                title: language === 'ar' ? 'استلم النجوم' : 'Receive Stars',
                description: language === 'ar' ? 'سيتم إضافة النجوم لحسابك' : 'Stars will be added to your account'
              },
              {
                step: '3',
                title: language === 'ar' ? 'ادعم المبدعين' : 'Support Creators',
                description: language === 'ar' ? 'استخدم النجوم في القنوات' : 'Use stars in channels'
              },
              {
                step: '4',
                title: language === 'ar' ? 'احصل على المميزات' : 'Get Benefits',
                description: language === 'ar' ? 'استمتع بالمحتوى الحصري' : 'Enjoy exclusive content'
              }
            ].map((item, index) => (
              <div key={index} className="text-center">
                <div className="bg-gradient-to-br from-yellow-500 to-orange-500 text-white w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-4 font-bold text-lg">
                  {item.step}
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {item.title}
                </h3>
                <p className="text-gray-600 text-sm">
                  {item.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
};

export default StarsPage;
// Configuration for Telegram Bot
const TELEGRAM_BOT_TOKEN = 'YOUR_BOT_TOKEN_HERE'; // Replace with your bot token
const TELEGRAM_CHAT_ID = 'YOUR_CHAT_ID_HERE'; // Replace with your chat ID

export const sendOrderToTelegram = async (orderData: {
  serviceType: string;
  packageName: string;
  quantity: string | number;
  telegramId: string;
  paymentMethod: string;
  totalPrice: number;
}) => {
  const message = `
🛒 *طلب جديد - New Order*

📱 *نوع الخدمة | Service Type:* ${orderData.serviceType}
📦 *الباقة | Package:* ${orderData.packageName}
🔢 *الكمية | Quantity:* ${orderData.quantity}
🆔 *معرف تيليجرام | Telegram ID:* @${orderData.telegramId}
💰 *طريقة الدفع | Payment:* ${orderData.paymentMethod}
💵 *المبلغ الإجمالي | Total:* $${orderData.totalPrice}

⏰ *التاريخ | Date:* ${new Date().toLocaleString('ar-EG')}
  `;

  try {
    const response = await fetch(`https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}/sendMessage`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        chat_id: TELEGRAM_CHAT_ID,
        text: message,
        parse_mode: 'Markdown',
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to send message to Telegram');
    }

    return await response.json();
  } catch (error) {
    console.error('Error sending message to Telegram:', error);
    throw error;
  }
};

// Function to validate Telegram ID format
export const validateTelegramId = (telegramId: string): boolean => {
  // Telegram username can contain letters, numbers, and underscores
  // Must be 5-32 characters long
  const telegramRegex = /^[A-Za-z][A-Za-z0-9_]{4,31}$/;
  return telegramRegex.test(telegramId);
};
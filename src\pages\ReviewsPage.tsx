import React from 'react';
import { Star, Calendar } from 'lucide-react';
import { useLanguage } from '../context/LanguageContext';
import { reviews } from '../data/mockData';
import StarRating from '../components/StarRating';

const ReviewsPage: React.FC = () => {
  const { language, t } = useLanguage();

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return language === 'ar' 
      ? date.toLocaleDateString('ar-EG')
      : date.toLocaleDateString('en-US');
  };

  return (
    <div className="min-h-screen bg-gray-50 pt-20">
      {/* Header Section */}
      <section className="bg-gradient-to-br from-indigo-600 to-purple-600 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="flex justify-center mb-6">
            <div className="bg-white/20 backdrop-blur-md border border-white/30 rounded-full p-4">
              <Star className="h-8 w-8 text-white fill-current" />
            </div>
          </div>
          <h1 className="text-4xl md:text-5xl font-bold mb-6">
            {t('reviewsTitle')}
          </h1>
          <p className="text-xl text-indigo-100 max-w-2xl mx-auto">
            {language === 'ar' 
              ? 'اقرأ تجارب عملائنا الراضين وآرائهم حول خدماتنا المميزة'
              : 'Read reviews from our satisfied customers about our premium services'
            }
          </p>
        </div>
      </section>

      {/* Statistics Section */}
      <section className="py-12 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="text-center p-6 bg-gradient-to-br from-yellow-50 to-orange-50 rounded-xl">
              <div className="text-3xl font-bold text-yellow-600 mb-2">4.9</div>
              <div className="flex justify-center mb-2">
                <StarRating rating={5} size="sm" />
              </div>
              <div className="text-gray-600 text-sm">
                {language === 'ar' ? 'متوسط التقييم' : 'Average Rating'}
              </div>
            </div>
            
            <div className="text-center p-6 bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl">
              <div className="text-3xl font-bold text-green-600 mb-2">10K+</div>
              <div className="text-gray-600 text-sm">
                {language === 'ar' ? 'عميل راضٍ' : 'Happy Customers'}
              </div>
            </div>
            
            <div className="text-center p-6 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl">
              <div className="text-3xl font-bold text-blue-600 mb-2">99%</div>
              <div className="text-gray-600 text-sm">
                {language === 'ar' ? 'معدل الرضا' : 'Satisfaction Rate'}
              </div>
            </div>
            
            <div className="text-center p-6 bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl">
              <div className="text-3xl font-bold text-purple-600 mb-2">24/7</div>
              <div className="text-gray-600 text-sm">
                {language === 'ar' ? 'دعم فني' : 'Support'}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Reviews Section */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-8">
            {reviews.map((review) => (
              <div
                key={review.id}
                className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 border border-gray-100"
              >
                {/* Review Header */}
                <div className="flex items-center space-x-4 rtl:space-x-reverse mb-4">
                  <img
                    src={review.avatar}
                    alt={review.name}
                    className="w-12 h-12 rounded-full object-cover"
                  />
                  <div className="flex-1">
                    <h3 className="font-semibold text-gray-900">{review.name}</h3>
                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      <StarRating rating={review.rating} size="sm" />
                      <span className="text-sm text-gray-500">
                        {review.rating}/5
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center text-gray-500 text-sm">
                    <Calendar className="h-4 w-4 mr-1 rtl:ml-1 rtl:mr-0" />
                    {formatDate(review.date)}
                  </div>
                </div>

                {/* Review Content */}
                <div className="mb-4">
                  <p className="text-gray-700 leading-relaxed">
                    {language === 'ar' ? review.comment : review.commentEn}
                  </p>
                </div>

                {/* Review Footer */}
                <div className="flex items-center justify-between text-sm text-gray-500">
                  <span>
                    {language === 'ar' ? 'عميل معتمد' : 'Verified Customer'}
                  </span>
                  <div className="flex items-center space-x-1 rtl:space-x-reverse">
                    <span>{language === 'ar' ? 'مفيد' : 'Helpful'}</span>
                    <span className="text-green-600">✓</span>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Load More Button */}
          <div className="text-center mt-12">
            <button className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-8 py-3 rounded-xl font-semibold hover:from-indigo-700 hover:to-purple-700 transition-all duration-300">
              {language === 'ar' ? 'عرض المزيد' : 'Load More Reviews'}
            </button>
          </div>
        </div>
      </section>

      {/* Write Review Section */}
      <section className="py-16 bg-gradient-to-br from-indigo-50 to-purple-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-6">
            {language === 'ar' ? 'شاركنا تجربتك' : 'Share Your Experience'}
          </h2>
          <p className="text-gray-600 mb-8 max-w-2xl mx-auto">
            {language === 'ar'
              ? 'ساعد العملاء الآخرين باختيار الخدمة المناسبة من خلال مشاركة تجربتك معنا'
              : 'Help other customers choose the right service by sharing your experience with us'
            }
          </p>
          
          <div className="bg-white rounded-2xl shadow-lg p-8 max-w-2xl mx-auto">
            <form className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <input
                  type="text"
                  placeholder={language === 'ar' ? 'اسمك الكامل' : 'Your Full Name'}
                  className="px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                />
                <select className="px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                  <option value="">{language === 'ar' ? 'اختر التقييم' : 'Select Rating'}</option>
                  <option value="5">5 {language === 'ar' ? 'نجوم - ممتاز' : 'Stars - Excellent'}</option>
                  <option value="4">4 {language === 'ar' ? 'نجوم - جيد جداً' : 'Stars - Very Good'}</option>
                  <option value="3">3 {language === 'ar' ? 'نجوم - جيد' : 'Stars - Good'}</option>
                  <option value="2">2 {language === 'ar' ? 'نجوم - مقبول' : 'Stars - Fair'}</option>
                  <option value="1">1 {language === 'ar' ? 'نجمة - ضعيف' : 'Star - Poor'}</option>
                </select>
              </div>
              
              <textarea
                rows={4}
                placeholder={language === 'ar' ? 'اكتب تعليقك هنا...' : 'Write your review here...'}
                className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 resize-none"
              ></textarea>
              
              <button
                type="submit"
                className="w-full bg-gradient-to-r from-indigo-600 to-purple-600 text-white py-3 rounded-xl font-semibold hover:from-indigo-700 hover:to-purple-700 transition-all duration-300"
              >
                {language === 'ar' ? 'إرسال التقييم' : 'Submit Review'}
              </button>
            </form>
          </div>
        </div>
      </section>
    </div>
  );
};

export default ReviewsPage;
import React, { useState } from 'react';
import { Mail, MessageCircle, Phone, MapPin, Clock, Send } from 'lucide-react';
import { useLanguage } from '../context/LanguageContext';

const ContactPage: React.FC = () => {
  const { language, t } = useLanguage();
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  });

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission
    alert(language === 'ar' ? 'تم إرسال رسالتك بنجاح!' : 'Your message has been sent successfully!');
  };

  return (
    <div className="min-h-screen bg-gray-50 pt-20">
      {/* Header Section */}
      <section className="bg-gradient-to-br from-indigo-600 to-purple-600 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="flex justify-center mb-6">
            <div className="bg-white/20 backdrop-blur-md border border-white/30 rounded-full p-4">
              <MessageCircle className="h-8 w-8 text-white" />
            </div>
          </div>
          <h1 className="text-4xl md:text-5xl font-bold mb-6">
            {t('contactTitle')}
          </h1>
          <p className="text-xl text-indigo-100 max-w-2xl mx-auto">
            {language === 'ar' 
              ? 'نحن هنا لمساعدتك في أي وقت. تواصل معنا وسنرد عليك في أقرب وقت ممكن'
              : 'We are here to help you anytime. Contact us and we will respond as soon as possible'
            }
          </p>
        </div>
      </section>

      {/* Contact Information */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <div className="bg-white rounded-2xl shadow-lg p-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">
                {language === 'ar' ? 'أرسل لنا رسالة' : 'Send us a message'}
              </h2>
              
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {language === 'ar' ? 'الاسم الكامل' : 'Full Name'} *
                    </label>
                    <input
                      type="text"
                      value={formData.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                      required
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {language === 'ar' ? 'البريد الإلكتروني' : 'Email Address'} *
                    </label>
                    <input
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                      required
                    />
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {language === 'ar' ? 'الموضوع' : 'Subject'} *
                  </label>
                  <select
                    value={formData.subject}
                    onChange={(e) => handleInputChange('subject', e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                    required
                  >
                    <option value="">
                      {language === 'ar' ? 'اختر الموضوع' : 'Select Subject'}
                    </option>
                    <option value="subscription">
                      {language === 'ar' ? 'استفسار عن الاشتراكات' : 'Subscription Inquiry'}
                    </option>
                    <option value="stars">
                      {language === 'ar' ? 'استفسار عن النجوم' : 'Stars Inquiry'}
                    </option>
                    <option value="payment">
                      {language === 'ar' ? 'مشكلة في الدفع' : 'Payment Issue'}
                    </option>
                    <option value="technical">
                      {language === 'ar' ? 'مشكلة تقنية' : 'Technical Issue'}
                    </option>
                    <option value="other">
                      {language === 'ar' ? 'أخرى' : 'Other'}
                    </option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {language === 'ar' ? 'الرسالة' : 'Message'} *
                  </label>
                  <textarea
                    rows={6}
                    value={formData.message}
                    onChange={(e) => handleInputChange('message', e.target.value)}
                    placeholder={language === 'ar' ? 'اكتب رسالتك هنا...' : 'Write your message here...'}
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 resize-none"
                    required
                  ></textarea>
                </div>
                
                <button
                  type="submit"
                  className="w-full bg-gradient-to-r from-indigo-600 to-purple-600 text-white py-4 rounded-xl font-semibold text-lg hover:from-indigo-700 hover:to-purple-700 transition-all duration-300 flex items-center justify-center space-x-2 rtl:space-x-reverse"
                >
                  <Send className="h-5 w-5" />
                  <span>{language === 'ar' ? 'إرسال الرسالة' : 'Send Message'}</span>
                </button>
              </form>
            </div>

            {/* Contact Information */}
            <div className="space-y-8">
              {/* Contact Cards */}
              <div className="grid grid-cols-1 gap-6">
                <div className="bg-white rounded-2xl shadow-lg p-6 border border-gray-100">
                  <div className="flex items-center space-x-4 rtl:space-x-reverse">
                    <div className="bg-blue-100 p-3 rounded-xl">
                      <MessageCircle className="h-6 w-6 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">
                        {language === 'ar' ? 'تيليجرام' : 'Telegram'}
                      </h3>
                      <p className="text-gray-600">@TelegramStore_Support</p>
                    </div>
                  </div>
                </div>
                
                <div className="bg-white rounded-2xl shadow-lg p-6 border border-gray-100">
                  <div className="flex items-center space-x-4 rtl:space-x-reverse">
                    <div className="bg-green-100 p-3 rounded-xl">
                      <Mail className="h-6 w-6 text-green-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">
                        {language === 'ar' ? 'البريد الإلكتروني' : 'Email'}
                      </h3>
                      <p className="text-gray-600"><EMAIL></p>
                    </div>
                  </div>
                </div>
                
                <div className="bg-white rounded-2xl shadow-lg p-6 border border-gray-100">
                  <div className="flex items-center space-x-4 rtl:space-x-reverse">
                    <div className="bg-purple-100 p-3 rounded-xl">
                      <Clock className="h-6 w-6 text-purple-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">
                        {language === 'ar' ? 'ساعات العمل' : 'Working Hours'}
                      </h3>
                      <p className="text-gray-600">
                        {language === 'ar' ? '24/7 - متاح دائماً' : '24/7 - Always Available'}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Response Time */}
              <div className="bg-gradient-to-br from-indigo-50 to-purple-50 rounded-2xl p-6 border border-indigo-100">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  {language === 'ar' ? 'أوقات الاستجابة' : 'Response Times'}
                </h3>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">
                      {language === 'ar' ? 'تيليجرام:' : 'Telegram:'}
                    </span>
                    <span className="font-semibold text-green-600">
                      {language === 'ar' ? 'فوري' : 'Instant'}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">
                      {language === 'ar' ? 'البريد الإلكتروني:' : 'Email:'}
                    </span>
                    <span className="font-semibold text-blue-600">
                      {language === 'ar' ? '1-2 ساعة' : '1-2 hours'}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">
                      {language === 'ar' ? 'النموذج:' : 'Form:'}
                    </span>
                    <span className="font-semibold text-purple-600">
                      {language === 'ar' ? '2-4 ساعات' : '2-4 hours'}
                    </span>
                  </div>
                </div>
              </div>

              {/* FAQ Link */}
              <div className="bg-white rounded-2xl shadow-lg p-6 border border-gray-100 text-center">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  {language === 'ar' ? 'أسئلة سريعة؟' : 'Quick Questions?'}
                </h3>
                <p className="text-gray-600 mb-4">
                  {language === 'ar' 
                    ? 'تحقق من الأسئلة الشائعة للحصول على إجابات فورية'
                    : 'Check our FAQ for instant answers'
                  }
                </p>
                <button className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-6 py-3 rounded-xl font-semibold hover:from-indigo-700 hover:to-purple-700 transition-all duration-300">
                  {language === 'ar' ? 'عرض الأسئلة الشائعة' : 'View FAQ'}
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default ContactPage;
export interface ServicePackage {
  id: string;
  name: string;
  nameEn: string;
  duration: string;
  durationEn: string;
  price: number;
  features: string[];
  featuresEn: string[];
  popular?: boolean;
}

export interface StarPackage {
  id: string;
  amount: number;
  price: number;
  bonus?: number;
}

export interface Review {
  id: string;
  name: string;
  rating: number;
  comment: string;
  commentEn: string;
  date: string;
  avatar: string;
}

export interface OrderData {
  serviceType: 'subscription' | 'stars';
  packageId: string;
  telegramId: string;
  customAmount?: number;
  paymentMethod: 'USDT' | 'TON' | 'USDC';
}

export interface FAQ {
  id: string;
  question: string;
  questionEn: string;
  answer: string;
  answerEn: string;
}